package com.layaa.skinlib.helper;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.layaa.libutils.module_log.LogUtils;
import com.layaa.skinlib.R;
import com.layaa.skinlib.SkinKit;

import androidx.core.content.res.ResourcesCompat;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
public class SkinCompatTextHelper extends BaseSkinCompatHelper {
    private static final String TAG = SkinCompatTextHelper.class.getSimpleName();

    public static SkinCompatTextHelper create(TextView textView) {
        return new SkinCompatTextHelper(textView);
    }

    final TextView mView;

    private String mResName;
    private int mResTextId;

    private String mHintName;
    private int mHintId;

    // Drawable resources
    private int mDrawableStartId;
    private int mDrawableEndId;
    private int mDrawableTopId;
    private int mDrawableBottomId;

    // Compat drawable resources
    private int mDrawableStartCompatId;
    private int mDrawableEndCompatId;
    private int mDrawableTopCompatId;
    private int mDrawableBottomCompatId;

    public SkinCompatTextHelper(TextView view) {
        mView = view;
    }

    @Override
    public void loadFromAttributes(AttributeSet attrs, int defStyleAttr) {
        final Context context = mView.getContext();

        // First read the TextAppearance style id
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.SkinCompatTextHelper, defStyleAttr, 0);


        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_text)) {
            mResTextId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_text, INVALID_ID);
            if (mResTextId != INVALID_ID) {
                mResName = context.getResources().getResourceEntryName(mResTextId);
            }

        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_hint)) {
            mHintId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_hint, INVALID_ID);
            if (mHintId != INVALID_ID) {
                mHintName = context.getResources().getResourceEntryName(mHintId);
            }
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableStart)) {
            mDrawableStartId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableStart, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableEnd)) {
            mDrawableEndId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableEnd, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableTop)) {
            mDrawableTopId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableTop, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableBottom)) {
            mDrawableBottomId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableBottom, INVALID_ID);
        }

        // Handle compat drawable attributes
        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableStartCompat)) {
            mDrawableStartCompatId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableStartCompat, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableEndCompat)) {
            mDrawableEndCompatId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableEndCompat, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableTopCompat)) {
            mDrawableTopCompatId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableTopCompat, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableBottomCompat)) {
            mDrawableBottomCompatId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableBottomCompat, INVALID_ID);
        }

        a.recycle();
        applySkin();
    }

    public void setText(int resId) {
        mResTextId = resId;
        if (mResTextId != INVALID_ID) {
            mResName = mView.getResources().getResourceEntryName(mResTextId);
        }
    }

    public void setDrawableStartCompat(int resId) {
        mDrawableStartCompatId = resId;
        applySkin();
    }

    public void setDrawableEndCompat(int resId) {
        mDrawableEndCompatId = resId;
        applySkin();
    }

    public void setDrawableTopCompat(int resId) {
        mDrawableTopCompatId = resId;
        applySkin();
    }

    public void setDrawableBottomCompat(int resId) {
        mDrawableBottomCompatId = resId;
        applySkin();
    }


    private void applyText() {

        if (!TextUtils.isEmpty(mResName)) {
            mView.setText(SkinKit.getInstance().getString(mView.getContext(), mResName, mResTextId));
        }

        if (!TextUtils.isEmpty(mHintName)) {
            mView.setHint(SkinKit.getInstance().getString(mView.getContext(), mHintName, mHintId));
        }

        if (SkinKit.getInstance().isRtl()) {
            mView.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
            mView.setTextDirection(View.TEXT_DIRECTION_RTL);
        } else {
            mView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
            mView.setTextDirection(View.TEXT_DIRECTION_LTR);
        }
    }

    private void applyDrawables() {
        try {
            Context context = mView.getContext();
            boolean isRtl = SkinKit.getInstance().isRtl();

            // Get current drawables
            Drawable[] drawables = mView.getCompoundDrawables();
            Drawable drawableStart = drawables[0];
            Drawable drawableTop = drawables[1];
            Drawable drawableEnd = drawables[2];
            Drawable drawableBottom = drawables[3];

            // Apply drawable start (prefer compat version)
            int startId = mDrawableStartCompatId != INVALID_ID ? mDrawableStartCompatId : mDrawableStartId;
            if (startId != INVALID_ID) {
                drawableStart = ResourcesCompat.getDrawable(context.getResources(), startId, null);
            }

            // Apply drawable end (prefer compat version)
            int endId = mDrawableEndCompatId != INVALID_ID ? mDrawableEndCompatId : mDrawableEndId;
            if (endId != INVALID_ID) {
                drawableEnd = ResourcesCompat.getDrawable(context.getResources(), endId, null);
            }

            // Apply drawable top (prefer compat version)
            int topId = mDrawableTopCompatId != INVALID_ID ? mDrawableTopCompatId : mDrawableTopId;
            if (topId != INVALID_ID) {
                drawableTop = ResourcesCompat.getDrawable(context.getResources(), topId, null);
            }

            // Apply drawable bottom (prefer compat version)
            int bottomId = mDrawableBottomCompatId != INVALID_ID ? mDrawableBottomCompatId : mDrawableBottomId;
            if (bottomId != INVALID_ID) {
                drawableBottom = ResourcesCompat.getDrawable(context.getResources(), bottomId, null);
            }

            // Set compound drawables
            mView.setCompoundDrawablesRelative(drawableStart, drawableTop, drawableEnd, drawableBottom);

        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    @Override
    public void applySkin() {
        applyText();
        applyDrawables();
    }
}
