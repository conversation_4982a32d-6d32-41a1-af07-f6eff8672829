package com.layaa.skinlib.helper;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.layaa.skinlib.R;
import com.layaa.skinlib.SkinKit;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
public class SkinCompatTextHelper extends BaseSkinCompatHelper {
    private static final String TAG = SkinCompatTextHelper.class.getSimpleName();

    public static SkinCompatTextHelper create(TextView textView) {
        return new SkinCompatTextHelper(textView);
    }

    final TextView mView;

    private String mResName;
    private int mResTextId;

    private String mHintName;
    private int mHintId;

    // Drawable resources
    private String mDrawableStartName;
    private int mDrawableStartId;
    private String mDrawableEndName;
    private int mDrawableEndId;
    private String mDrawableTopName;
    private int mDrawableTopId;
    private String mDrawableBottomName;
    private int mDrawableBottomId;

    public SkinCompatTextHelper(TextView view) {
        mView = view;
    }

    @Override
    public void loadFromAttributes(AttributeSet attrs, int defStyleAttr) {
        final Context context = mView.getContext();

        // First read the TextAppearance style id
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.SkinCompatTextHelper, defStyleAttr, 0);


        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_text)) {
            mResTextId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_text, INVALID_ID);
            if (mResTextId != INVALID_ID) {
                mResName = context.getResources().getResourceEntryName(mResTextId);
            }

        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_hint)) {
            mHintId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_hint, INVALID_ID);
            if (mHintId != INVALID_ID) {
                mHintName = context.getResources().getResourceEntryName(mHintId);
            }
        }

        // Handle drawable attributes
        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableStart)) {
            mDrawableStartId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableStart, INVALID_ID);
            if (mDrawableStartId != INVALID_ID) {
                mDrawableStartName = context.getResources().getResourceEntryName(mDrawableStartId);
            }
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableEnd)) {
            mDrawableEndId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableEnd, INVALID_ID);
            if (mDrawableEndId != INVALID_ID) {
                mDrawableEndName = context.getResources().getResourceEntryName(mDrawableEndId);
            }
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableTop)) {
            mDrawableTopId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableTop, INVALID_ID);
            if (mDrawableTopId != INVALID_ID) {
                mDrawableTopName = context.getResources().getResourceEntryName(mDrawableTopId);
            }
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableBottom)) {
            mDrawableBottomId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableBottom, INVALID_ID);
            if (mDrawableBottomId != INVALID_ID) {
                mDrawableBottomName = context.getResources().getResourceEntryName(mDrawableBottomId);
            }
        }

        a.recycle();
        applySkin();
    }

    public void setText(int resId) {
        mResTextId = resId;
        if (mResTextId != INVALID_ID) {
            mResName = mView.getResources().getResourceEntryName(mResTextId);
        }
    }

    public void setDrawableStart(int resId) {
        mDrawableStartId = resId;
        if (mDrawableStartId != INVALID_ID) {
            mDrawableStartName = mView.getResources().getResourceEntryName(mDrawableStartId);
        }
        applySkin();
    }

    public void setDrawableEnd(int resId) {
        mDrawableEndId = resId;
        if (mDrawableEndId != INVALID_ID) {
            mDrawableEndName = mView.getResources().getResourceEntryName(mDrawableEndId);
        }
        applySkin();
    }

    public void setDrawableTop(int resId) {
        mDrawableTopId = resId;
        if (mDrawableTopId != INVALID_ID) {
            mDrawableTopName = mView.getResources().getResourceEntryName(mDrawableTopId);
        }
        applySkin();
    }

    public void setDrawableBottom(int resId) {
        mDrawableBottomId = resId;
        if (mDrawableBottomId != INVALID_ID) {
            mDrawableBottomName = mView.getResources().getResourceEntryName(mDrawableBottomId);
        }
        applySkin();
    }


    private void applyText() {

        if (!TextUtils.isEmpty(mResName)) {
            mView.setText(SkinKit.getInstance().getString(mView.getContext(), mResName, mResTextId));
        }

        if (!TextUtils.isEmpty(mHintName)) {
            mView.setHint(SkinKit.getInstance().getString(mView.getContext(), mHintName, mHintId));
        }

        // Apply drawables
        applyDrawables();

        if (SkinKit.getInstance().isRtl()) {
            mView.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
            mView.setTextDirection(View.TEXT_DIRECTION_RTL);
        } else {
            mView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
            mView.setTextDirection(View.TEXT_DIRECTION_LTR);
        }
    }

    private void applyDrawables() {
        try {
            Context context = mView.getContext();
            boolean isRtl = SkinKit.getInstance().isRtl();

            // Get current drawables
            Drawable[] drawables = mView.getCompoundDrawables();
            Drawable drawableLeft = drawables[0];
            Drawable drawableTop = drawables[1];
            Drawable drawableRight = drawables[2];
            Drawable drawableBottom = drawables[3];

            // Apply drawable start (left in LTR, right in RTL)
            if (!TextUtils.isEmpty(mDrawableStartName) && mDrawableStartId != INVALID_ID) {
                Drawable drawable = context.getResources().getDrawable(mDrawableStartId, null);
                if (isRtl) {
                    drawableRight = drawable;
                } else {
                    drawableLeft = drawable;
                }
            }

            // Apply drawable end (right in LTR, left in RTL)
            if (!TextUtils.isEmpty(mDrawableEndName) && mDrawableEndId != INVALID_ID) {
                Drawable drawable = context.getResources().getDrawable(mDrawableEndId, null);
                if (isRtl) {
                    drawableLeft = drawable;
                } else {
                    drawableRight = drawable;
                }
            }

            // Apply drawable top
            if (!TextUtils.isEmpty(mDrawableTopName) && mDrawableTopId != INVALID_ID) {
                drawableTop = context.getResources().getDrawable(mDrawableTopId, null);
            }

            // Apply drawable bottom
            if (!TextUtils.isEmpty(mDrawableBottomName) && mDrawableBottomId != INVALID_ID) {
                drawableBottom = context.getResources().getDrawable(mDrawableBottomId, null);
            }

            // Set compound drawables
            mView.setCompoundDrawables(drawableLeft, drawableTop, drawableRight, drawableBottom);

        } catch (Exception e) {
            // Handle exception silently to avoid crashes
        }
    }

    @Override
    public void applySkin() {
        applyText();
    }
}
