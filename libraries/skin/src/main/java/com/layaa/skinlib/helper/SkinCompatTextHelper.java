package com.layaa.skinlib.helper;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.layaa.libutils.module_log.LogUtils;
import com.layaa.skinlib.R;
import com.layaa.skinlib.SkinKit;

import androidx.core.content.res.ResourcesCompat;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
public class SkinCompatTextHelper extends BaseSkinCompatHelper {
    private static final String TAG = SkinCompatTextHelper.class.getSimpleName();

    public static SkinCompatTextHelper create(TextView textView) {
        return new SkinCompatTextHelper(textView);
    }

    final TextView mView;

    private String mResName;
    private int mResTextId;

    private String mHintName;
    private int mHintId;

    // Drawable resources (supports both standard and compat attributes)
    private int mDrawableStartId;
    private int mDrawableEndId;
    private int mDrawableTopId;
    private int mDrawableBottomId;

    public SkinCompatTextHelper(TextView view) {
        mView = view;
    }

    @Override
    public void loadFromAttributes(AttributeSet attrs, int defStyleAttr) {
        final Context context = mView.getContext();

        // First read the TextAppearance style id
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.SkinCompatTextHelper, defStyleAttr, 0);


        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_text)) {
            mResTextId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_text, INVALID_ID);
            if (mResTextId != INVALID_ID) {
                mResName = context.getResources().getResourceEntryName(mResTextId);
            }

        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_hint)) {
            mHintId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_hint, INVALID_ID);
            if (mHintId != INVALID_ID) {
                mHintName = context.getResources().getResourceEntryName(mHintId);
            }
        }

        // Handle drawable attributes (standard first, then compat overrides)
        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableStart)) {
            mDrawableStartId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableStart, INVALID_ID);
        }
        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableStartCompat)) {
            mDrawableStartId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableStartCompat, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableEnd)) {
            mDrawableEndId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableEnd, INVALID_ID);
        }
        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableEndCompat)) {
            mDrawableEndId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableEndCompat, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableTop)) {
            mDrawableTopId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableTop, INVALID_ID);
        }
        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableTopCompat)) {
            mDrawableTopId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableTopCompat, INVALID_ID);
        }

        if (a.hasValue(R.styleable.SkinCompatTextHelper_android_drawableBottom)) {
            mDrawableBottomId = a.getResourceId(R.styleable.SkinCompatTextHelper_android_drawableBottom, INVALID_ID);
        }
        if (a.hasValue(R.styleable.SkinCompatTextHelper_drawableBottomCompat)) {
            mDrawableBottomId = a.getResourceId(R.styleable.SkinCompatTextHelper_drawableBottomCompat, INVALID_ID);
        }

        a.recycle();
        applySkin();
    }

    public void setText(int resId) {
        mResTextId = resId;
        if (mResTextId != INVALID_ID) {
            mResName = mView.getResources().getResourceEntryName(mResTextId);
        }
    }

    private void applyText() {

        if (!TextUtils.isEmpty(mResName)) {
            mView.setText(SkinKit.getInstance().getString(mView.getContext(), mResName, mResTextId));
        }

        if (!TextUtils.isEmpty(mHintName)) {
            mView.setHint(SkinKit.getInstance().getString(mView.getContext(), mHintName, mHintId));
        }

        if (SkinKit.getInstance().isRtl()) {
            mView.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
            mView.setTextDirection(View.TEXT_DIRECTION_RTL);
        } else {
            mView.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
            mView.setTextDirection(View.TEXT_DIRECTION_LTR);
        }
    }

    private void applyDrawables() {
        try {
            Context context = mView.getContext();

            // Initialize drawables to null (clear existing ones)
            Drawable drawableStart = null;
            Drawable drawableTop = null;
            Drawable drawableEnd = null;
            Drawable drawableBottom = null;

            // Apply drawable start - system will automatically choose ldrtl version if available
            if (mDrawableStartId != INVALID_ID) {
                drawableStart = ResourcesCompat.getDrawable(context.getResources(), mDrawableStartId, null);
            }

            // Apply drawable end - system will automatically choose ldrtl version if available
            if (mDrawableEndId != INVALID_ID) {
                drawableEnd = ResourcesCompat.getDrawable(context.getResources(), mDrawableEndId, null);
            }

            // Apply drawable top
            if (mDrawableTopId != INVALID_ID) {
                drawableTop = ResourcesCompat.getDrawable(context.getResources(), mDrawableTopId, null);
            }

            // Apply drawable bottom
            if (mDrawableBottomId != INVALID_ID) {
                drawableBottom = ResourcesCompat.getDrawable(context.getResources(), mDrawableBottomId, null);
            }

            // Use setCompoundDrawablesRelativeWithIntrinsicBounds to support RTL automatically
            mView.setCompoundDrawablesRelativeWithIntrinsicBounds(drawableStart, drawableTop, drawableEnd, drawableBottom);

        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    @Override
    public void applySkin() {
        applyText();
        applyDrawables();
    }
}
