package com.layaa.accountlogic.profile.self

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.layaa.language.LanguageController
import com.layaa.skinlib.SkinCompatSupportable
import com.layaa.skinlib.SkinKit

/**
 * 语言切换辅助类
 * 用于处理语言切换时的View更新，特别是RTL/LTR布局和图片资源
 * 
 * <AUTHOR>
 * @date 2023/12/6
 */
object LanguageSwitchHelper {
    
    /**
     * 为View应用语言切换后的更新
     * 包括RTL/LTR布局方向和相应的图片资源
     */
    fun applyLanguageChange(rootView: View) {
        applyToViewHierarchy(rootView)
    }
    
    /**
     * 递归应用到View层次结构
     */
    private fun applyToViewHierarchy(view: View) {
        if (view is SkinCompatSupportable) {
            view.applySkin()
        }
        
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                applyToViewHierarchy(view.getChildAt(i))
            }
        }
    }
    
}
