package com.layaa.play

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewStub
import androidx.core.app.NotificationManagerCompat
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.alibaba.android.arouter.facade.annotation.Route
import com.layaa.accountapi.bean.BannedData
import com.layaa.accountapi.event.LogoutEvent
import com.layaa.accountapi.login.LoginPath
import com.layaa.accountapi.login.LoginRouter
import com.layaa.chatapi.ChatRouter
import com.layaa.chatapi.ImEvent
import com.layaa.chatapi.bean.MessageUnRed
import com.layaa.chatapi.dialog.DialogRouter
import com.layaa.chatapi.observer.DialogObserver
import com.layaa.chatapi.observer.EventObserver
import com.layaa.gameapi.GameRouter
import com.layaa.gotoannotation.GotoRoute
import com.layaa.gotorouter.GotoApp
import com.layaa.language.LanguageNotification
import com.layaa.language.LanguageObserve
import com.layaa.libnet.util.Success
import com.layaa.libui.GotoConst
import com.layaa.libui.base.BaseBindingVMActivity
import com.layaa.libui.dialog.CommonTipDialog
import com.layaa.libutils.DeviceUtils
import com.layaa.libutils.JumpSettingUtil
import com.layaa.libutils.kv.KVDelegate
import com.layaa.libutils.toast.ToastUtils
import com.layaa.mbtiapi.MbtiRouter
import com.layaa.payclient.PayClient
import com.layaa.play.databinding.ActivityMainBinding
import com.layaa.play.entity.GameData
import com.layaa.roomapi.router.RoomRouter
import com.layaa.roomapi.router.RoomRouter.Companion.switchRoomPage
import com.layaa.widget.basepage.NetWeakViewProvider
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.json.JSONObject

@GotoRoute(host = GotoConst.Host.MAIN, path = LoginPath.PAGE_MAIN)
@Route(path = LoginPath.PAGE_MAIN)
class MainActivity : BaseBindingVMActivity<MainViewModel, ActivityMainBinding>(), DialogObserver,
    EventObserver {

    private var tabIndex = 0;
    private var mbtiTabIndex = tabIndex++
    private var gameTabIndex = tabIndex++
    private var roomTabIndex = tabIndex++
    private var messageTabIndex = tabIndex++
    private var profileTabIndex = tabIndex++

    private var backGameDialog: CommonTipDialog? = null

    private val notifyPermissionDialog: CommonTipDialog by lazy {
        val notifyDialog = CommonTipDialog(this)
        notifyDialog.setContent(getString(R.string.permission_notification_tips))
        notifyDialog.setConfirmTxt(getString(R.string.settings_page_title))
        notifyDialog.confirmCallback = {
            JumpSettingUtil.goNotificationPage()
        }
        notifyDialog
    }

    private val keyNotifyPermission = "key_notify_permission"

    private val adapter by lazy {
        Adapter()
    }

    override fun inflateLayout(layoutInflater: LayoutInflater): ActivityMainBinding {
        return ActivityMainBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        initNetWeakViewProvider()
        super.onCreate(savedInstanceState)
    }

    private fun initNetWeakViewProvider() {
        NetWeakViewProvider.getNetWeakView = { context: Context ->
            LayoutInflater.from(context).inflate(R.layout.layout_net_error, null) as ViewStub
        }
    }


    override fun initData() {
        ChatRouter.login(LoginRouter.getUserId(), LoginRouter.getIMToken(), null)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        checkRegisterComplete()
    }

    private fun checkRegisterComplete() {
        if (LoginRouter.needCompleteRegister()) {
            LoginRouter.gotoCompleteRegister()
        }
    }

    override fun initView() {
        binding.viewpager.isUserInputEnabled = false
        binding.viewpager.adapter = adapter
        binding.viewpager.offscreenPageLimit = adapter.itemCount
    }

    override fun initEvent() {
        binding.mbti.setOnClickListener {
            binding.viewpager.setCurrentItem(mbtiTabIndex, false)
            selectPosition()
        }

        binding.game.setOnClickListener {
            binding.viewpager.setCurrentItem(gameTabIndex, false)
            selectPosition()
        }

        binding.room.setOnClickListener {
            binding.viewpager.setCurrentItem(roomTabIndex, false)
            selectPosition()
        }

        binding.message.setOnClickListener {
            binding.viewpager.setCurrentItem(messageTabIndex, false)
            selectPosition()
        }

        binding.profile.setOnClickListener {
            binding.viewpager.setCurrentItem(profileTabIndex, false)
            selectPosition()
        }

        binding.viewpager.setCurrentItem(mbtiTabIndex, false)
        selectPosition()
        parseGoto(intent)
        DialogRouter.addObserver(TAG, this)
        ChatRouter.registerEvent(TAG, this)
        binding.message.setUnreadCount(DialogRouter.getUnReadCount())
        viewModel.updateSelfUserInfo()

        viewModel.gotoGame.observe(this) {
            if (it is Success) {
                showBackGameDialog(it.value)
            }
        }
        viewModel.assertGame.observe(this) {
            dismissLoading()
            if (it is Success) {
                val data = it.value
                if (data.isNoGame) {
                    ToastUtils.show(R.string.game_enter_error)
                }
                viewModel.gotoLastTeam(data)
            }
        }
        viewModel.queryGameResult("")

        checkNotifyPermission()
    }

    override fun onResume() {
        super.onResume()
        PayClient.instance.onPageResume()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        if (LoginRouter.needCompleteRegister()) {
            return
        }
        parseGoto(intent)
    }

    private fun selectPosition() {
        binding.mbti.setSelected(false)
        binding.game.setSelected(false)
        binding.room.setSelected(false)
        binding.message.setSelected(false)
        binding.profile.setSelected(false)
        val index = binding.viewpager.currentItem
        when (index) {
            mbtiTabIndex -> {
                binding.mbti.setSelected(true)
            }

            gameTabIndex -> {
                binding.game.setSelected(true)
            }

            roomTabIndex -> {
                binding.room.setSelected(true)
            }

            messageTabIndex -> {
                binding.message.setSelected(true)
            }

            else -> {
                binding.profile.setSelected(true)
            }
        }
        /* else if (lastIndex == MainPagerAdapter.INDEX_ROOM) {
             binding.room.setSelected(true)
         }*/

    }

    private inner class Adapter : FragmentStateAdapter(this) {
        override fun getItemCount(): Int {
            return 5
        }

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                mbtiTabIndex -> {
                    MbtiRouter.getTestFragment()
                }

                gameTabIndex -> {
                    GameRouter.getGameFragment()
                }

                roomTabIndex -> {
                    RoomRouter.getRoomList()
                }

                messageTabIndex -> {
                    ChatRouter.getMessagePagerFragment()
                }

                else -> {
                    LoginRouter.getMineFragment(null)
                }
            }
        }

    }

    override fun isStatusBarFontDarkColor(): Boolean {
        return false
    }

    protected override fun isNavigationBarHide(): Boolean {
        return true
    }

    protected override fun navigationBarOffsetView(): Int {
        return binding.bottomView.id
    }

    @Subscribe
    fun onEvent(event: LogoutEvent) {
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        DialogRouter.removeObserver(TAG)
        ChatRouter.unregisterEvent(TAG)
        RoomRouter.quitRoom("close main")
    }

    override fun received(id: Int, vararg arg: Any?) {
        if (id == 1) {
            binding.message.setUnreadCount(DialogRouter.getUnReadCount())
            EventBus.getDefault().post(MessageUnRed(DialogRouter.getUnReadCount()))
        }
    }

    override fun onReceiveEvent(event: Int, data: JSONObject?): Boolean {
        if (data == null) {
            return false
        }
        if (event == ImEvent.CustomSysMsg.TYPE_BANNED) {
            if (TextUtils.equals(data.optString("uid"), LoginRouter.getUserId())) {
                LoginRouter.logout(BannedData().apply {
                    banType = "0"
                    banText = data.optString("banText")
                })
            }
        } else if (event == ImEvent.CustomSysMsg.TYPE_DEVICE_BANNED) {
            if (TextUtils.equals(data.optString("deviceId"), DeviceUtils.getDeviceID())) {
                LoginRouter.logout(BannedData().apply {
                    banType = "1"
                    banText = data.optString("banText")
                })
            }
        }
        return false
    }

    private fun showBackGameDialog(data: GameData?) {
        if (data == null || data.isNoGame) {
            return
        }
        if (backGameDialog == null) {
            backGameDialog = CommonTipDialog(this)
            backGameDialog?.setTitle(getString(R.string.game_reconnect_title))
            backGameDialog?.setContent(getString(R.string.game_reconnect_tips))
            backGameDialog?.confirmCallback = { ->
                showLoading()
                viewModel.assertGameInfo()
                null
            }
            backGameDialog?.cancelCallback = { ->
                val gameData = backGameDialog?.tag as GameData
                viewModel.cancelTeam(gameData)
                null
            }

        }
        backGameDialog?.show(data)
    }

    private fun parseGoto(intent: Intent?) {
        val bundle = intent?.extras ?: return
        val homeTabIndex = bundle.getString(GotoConst.PARAM_SUB_PAGE)
        if (!TextUtils.isEmpty(homeTabIndex)) {
            when (homeTabIndex) {
                GotoConst.Main.TAB_HOME, GotoConst.Main.TAB_GAME -> binding.viewpager.setCurrentItem(
                    gameTabIndex,
                    false
                )

                GotoConst.Main.TAB_MESSAGE -> binding.viewpager.setCurrentItem(
                    messageTabIndex,
                    false
                )

                GotoConst.Main.TAB_ME -> binding.viewpager.setCurrentItem(
                    profileTabIndex,
                    false
                )

                GotoConst.Main.TAB_PARTY_CHAT -> {
                    binding.viewpager.currentItem = roomTabIndex
                    switchRoomPage(2)
                }

                GotoConst.Main.TAB_PARTY_MINE_RECENT -> {
                    binding.viewpager.currentItem = roomTabIndex
                    switchRoomPage(0, 0)
                }

                GotoConst.Main.TAB_PARTY_MINE_FOLLOW -> {
                    binding.viewpager.currentItem = roomTabIndex
                    switchRoomPage(0, 1)
                }
            }
            selectPosition()
        } else {
            bundle.getString(SplashActivity.NEXT_GOTO)?.let {
                GotoApp.navigate(it)
            }
        }
    }

    private fun checkNotifyPermission() {
        val hasShow = KVDelegate.getInstance().getBool(keyNotifyPermission, false)
        if (hasShow) {
            return
        }
        val isEnable =
            NotificationManagerCompat.from(AppKit.getContext()).areNotificationsEnabled()
        if (isEnable) {
            return
        }
        notifyPermissionDialog.show()
        KVDelegate.getInstance().save(keyNotifyPermission, true)
    }
}